.contact-page {
  padding: 2rem 0;
  min-height: calc(100vh - 140px);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-info {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  height: fit-content;
}

.contact-info h2 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.contact-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.contact-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.contact-item i {
  font-size: 1.5rem;
  color: #3498db;
  margin-top: 0.5rem;
  width: 25px;
}

.contact-item h4 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.contact-item p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.contact-form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.contact-form h2 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .contact-info,
  .contact-form {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contact-page {
    padding: 1rem 0;
  }
  
  .contact-content {
    margin: 0 1rem;
  }
  
  .contact-info,
  .contact-form {
    padding: 1rem;
  }
  
  .contact-info h2,
  .contact-form h2 {
    font-size: 1.5rem;
  }
}
