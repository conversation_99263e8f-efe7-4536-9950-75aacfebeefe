.product-detail-page {
  padding: 2rem 0;
  min-height: calc(100vh - 140px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 1rem;
  color: #666;
}

.loading-container i {
  font-size: 3rem;
  color: #3498db;
}

.breadcrumb {
  margin-bottom: 2rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb a {
  color: #3498db;
  text-decoration: none;
  transition: all 0.3s ease;
}

.breadcrumb a:hover {
  color: #2980b9;
}

.product-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.product-images .main-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-images .main-image img:hover {
  transform: scale(1.02);
}

.product-category {
  font-size: 0.9rem;
  color: #3498db;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.product-info h1 {
  font-size: 2.2rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stars {
  color: #f39c12;
  font-size: 1.1rem;
}

.rating-text {
  color: #666;
  font-size: 0.9rem;
}

.product-price {
  font-size: 2.5rem;
  color: #e74c3c;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.product-description {
  margin: 2rem 0;
  color: #666;
  line-height: 1.8;
  font-size: 1.1rem;
}

.product-options {
  margin: 2rem 0;
}

.option-group {
  margin-bottom: 1.5rem;
}

.option-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.option-group select,
.option-group input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.option-group select:focus,
.option-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.product-actions {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
}

.add-to-cart-btn,
.wishlist-btn {
  flex: 1;
  padding: 1.2rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-to-cart-btn {
  background: #3498db;
  color: white;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-2px);
}

.add-to-cart-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

.add-to-cart-btn.loading {
  background: #95a5a6;
}

.wishlist-btn {
  background: white;
  color: #e74c3c;
  border: 2px solid #e74c3c;
}

.wishlist-btn:hover {
  background: #e74c3c;
  color: white;
}

.product-features {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  color: #666;
}

.feature i {
  color: #3498db;
  font-size: 1.2rem;
  width: 20px;
}

.related-products {
  margin-top: 4rem;
}

.related-products h2 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  color: #2c3e50;
  font-weight: 700;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .product-detail {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1.5rem;
  }
  
  .product-images .main-image img {
    height: 400px;
  }
  
  .product-info h1 {
    font-size: 1.8rem;
  }
  
  .product-price {
    font-size: 2rem;
  }
  
  .product-actions {
    flex-direction: column;
  }
  
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 480px) {
  .product-detail-page {
    padding: 1rem 0;
  }
  
  .product-detail {
    padding: 1rem;
  }
  
  .product-images .main-image img {
    height: 300px;
  }
  
  .product-info h1 {
    font-size: 1.5rem;
  }
  
  .product-price {
    font-size: 1.8rem;
  }
  
  .breadcrumb {
    margin: 0 1rem 2rem;
    font-size: 0.9rem;
  }
}
