<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - StyleSnap</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><a href="index.html"><i class="fas fa-camera"></i> StyleSnap</a></h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html" class="active">Products</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="nav-actions">
                <button class="search-btn"><i class="fas fa-search"></i></button>
                <button class="cart-btn" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Products Section -->
    <main class="products-page">
        <div class="container">
            <div class="page-header">
                <h1>Our Products</h1>
                <p>Discover our complete collection of premium clothing</p>
            </div>

            <!-- Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label>Category:</label>
                    <select id="category-filter" onchange="applyFilters()">
                        <option value="all">All Categories</option>
                        <option value="men">Men</option>
                        <option value="women">Women</option>
                        <option value="accessories">Accessories</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Sort by:</label>
                    <select id="sort-filter" onchange="applyFilters()">
                        <option value="name">Name</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Price Range:</label>
                    <input type="range" id="price-range" min="0" max="300" value="300" onchange="applyFilters()">
                    <span>Up to $<span id="price-display">300</span></span>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="product-grid" id="products-grid">
                <!-- Products will be loaded dynamically -->
            </div>
        </div>
    </main>

    <!-- Shopping Cart Sidebar -->
    <div class="cart-sidebar" id="cart-sidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
            <button class="close-cart" onclick="toggleCart()"><i class="fas fa-times"></i></button>
        </div>
        <div class="cart-items" id="cart-items">
            <!-- Cart items will be loaded dynamically -->
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <strong>Total: $<span id="cart-total">0.00</span></strong>
            </div>
            <button class="checkout-btn" onclick="goToCheckout()">Checkout</button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>StyleSnap</h4>
                    <p>Your premier destination for fashion-forward clothing.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 StyleSnap. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/products.js"></script>
</body>
</html>
