.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.product-image {
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.out-of-stock {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #e74c3c;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.featured-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #f39c12;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.product-info {
  padding: 1.5rem;
}

.product-category {
  font-size: 0.8rem;
  color: #3498db;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.product-name {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.3;
}

.product-description {
  color: #666;
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stars {
  color: #f39c12;
  font-size: 0.9rem;
}

.review-count {
  font-size: 0.8rem;
  color: #666;
}

.product-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #666;
}

.sizes-available,
.colors-available {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.sizes-available::before {
  content: "📏";
}

.colors-available::before {
  content: "🎨";
}

.product-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: #e74c3c;
  margin-bottom: 1rem;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
}

.quick-add-btn,
.view-details-btn,
.out-of-stock-btn {
  flex: 1;
  padding: 0.8rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.quick-add-btn {
  background: #3498db;
  color: white;
}

.quick-add-btn:hover:not(:disabled) {
  background: #2980b9;
}

.quick-add-btn.loading {
  background: #95a5a6;
  cursor: not-allowed;
}

.view-details-btn {
  background: white;
  color: #3498db;
  border: 2px solid #3498db;
}

.view-details-btn:hover {
  background: #3498db;
  color: white;
}

.out-of-stock-btn {
  background: #95a5a6;
  color: white;
  cursor: not-allowed;
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .product-card {
    margin-bottom: 1rem;
  }
  
  .product-image img {
    height: 200px;
  }
  
  .product-info {
    padding: 1rem;
  }
  
  .product-actions {
    flex-direction: column;
  }
  
  .quick-add-btn,
  .view-details-btn {
    flex: none;
  }
}

@media (max-width: 480px) {
  .product-image img {
    height: 180px;
  }
  
  .product-name {
    font-size: 1rem;
  }
  
  .product-price {
    font-size: 1.2rem;
  }
}
