// Products page functionality
let filteredProducts = [];
let currentCategory = 'all';
let currentSort = 'name';
let maxPrice = 300;

document.addEventListener('DOMContentLoaded', function() {
    // Wait for products to load from main.js
    setTimeout(() => {
        initializeProductsPage();
    }, 100);
});

function initializeProductsPage() {
    // Get URL parameters
    const categoryParam = getUrlParameter('category');
    if (categoryParam) {
        currentCategory = categoryParam;
        document.getElementById('category-filter').value = categoryParam;
    }
    
    // Initialize price range display
    const priceRange = document.getElementById('price-range');
    const priceDisplay = document.getElementById('price-display');
    
    if (priceRange && priceDisplay) {
        priceRange.addEventListener('input', function() {
            priceDisplay.textContent = this.value;
            maxPrice = parseInt(this.value);
            applyFilters();
        });
    }
    
    // Load and display products
    applyFilters();
}

function applyFilters() {
    // Get filter values
    const categoryFilter = document.getElementById('category-filter');
    const sortFilter = document.getElementById('sort-filter');
    const priceRange = document.getElementById('price-range');
    
    if (categoryFilter) currentCategory = categoryFilter.value;
    if (sortFilter) currentSort = sortFilter.value;
    if (priceRange) maxPrice = parseInt(priceRange.value);
    
    // Filter products
    filteredProducts = products.filter(product => {
        const categoryMatch = currentCategory === 'all' || product.category === currentCategory;
        const priceMatch = product.price <= maxPrice;
        return categoryMatch && priceMatch;
    });
    
    // Sort products
    switch (currentSort) {
        case 'price-low':
            filteredProducts.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filteredProducts.sort((a, b) => b.price - a.price);
            break;
        case 'name':
        default:
            filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
            break;
    }
    
    displayProducts();
}

function displayProducts() {
    const productsGrid = document.getElementById('products-grid');
    if (!productsGrid) return;
    
    if (filteredProducts.length === 0) {
        productsGrid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                <i class="fas fa-search" style="font-size: 3rem; color: var(--text-light); margin-bottom: 1rem;"></i>
                <h3>No products found</h3>
                <p>Try adjusting your filters to see more products.</p>
            </div>
        `;
        return;
    }
    
    productsGrid.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
}

// Search functionality
function searchProducts(query) {
    if (!query) {
        applyFilters();
        return;
    }
    
    filteredProducts = products.filter(product => {
        const searchMatch = product.name.toLowerCase().includes(query.toLowerCase()) ||
                           product.description.toLowerCase().includes(query.toLowerCase()) ||
                           product.category.toLowerCase().includes(query.toLowerCase());
        const categoryMatch = currentCategory === 'all' || product.category === currentCategory;
        const priceMatch = product.price <= maxPrice;
        return searchMatch && categoryMatch && priceMatch;
    });
    
    displayProducts();
}

// Add search functionality to search button
document.addEventListener('DOMContentLoaded', function() {
    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            const query = prompt('Search for products:');
            if (query !== null) {
                searchProducts(query);
            }
        });
    }
});

// Quick add to cart with size and color selection
function quickAddToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    // For quick add, use default size and color
    const defaultSize = product.sizes[0];
    const defaultColor = product.colors[0];
    
    addToCart(productId, defaultSize, defaultColor, 1);
}

// Enhanced product card creation for products page
function createDetailedProductCard(product) {
    return `
        <div class="product-card" onclick="goToProduct(${product.id})">
            <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x300?text=${encodeURIComponent(product.name)}'">
            <div class="product-info">
                <h3>${product.name}</h3>
                <p>${product.description}</p>
                <div class="product-meta">
                    <span class="category-tag">${product.category.charAt(0).toUpperCase() + product.category.slice(1)}</span>
                    <span class="sizes-available">${product.sizes.length} sizes</span>
                </div>
                <div class="product-price">$${product.price.toFixed(2)}</div>
                <div class="product-actions">
                    <button class="add-to-cart" onclick="event.stopPropagation(); quickAddToCart(${product.id})">
                        <i class="fas fa-shopping-cart"></i> Quick Add
                    </button>
                    <button class="view-details" onclick="event.stopPropagation(); goToProduct(${product.id})">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Update the displayProducts function to use detailed cards
function displayProducts() {
    const productsGrid = document.getElementById('products-grid');
    if (!productsGrid) return;
    
    if (filteredProducts.length === 0) {
        productsGrid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                <i class="fas fa-search" style="font-size: 3rem; color: var(--text-light); margin-bottom: 1rem;"></i>
                <h3>No products found</h3>
                <p>Try adjusting your filters to see more products.</p>
            </div>
        `;
        return;
    }
    
    // Use detailed cards for products page, simple cards for home page
    const isProductsPage = window.location.pathname.includes('products.html');
    const cardHTML = isProductsPage ? 
        filteredProducts.map(product => createDetailedProductCard(product)).join('') :
        filteredProducts.map(product => createProductCard(product)).join('');
    
    productsGrid.innerHTML = cardHTML;
}

// Form validation utilities
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateRequired(value) {
    return value && value.trim().length > 0;
}

// Contact form handling
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('contact-name').value;
            const email = document.getElementById('contact-email').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;
            
            // Validate form
            if (!validateRequired(name) || !validateRequired(email) || 
                !validateRequired(subject) || !validateRequired(message)) {
                alert('Please fill in all required fields.');
                return;
            }
            
            if (!validateEmail(email)) {
                alert('Please enter a valid email address.');
                return;
            }
            
            // Simulate form submission
            alert('Thank you for your message! We\'ll get back to you soon.');
            contactForm.reset();
        });
    }
});

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Get cart total
function getCartTotal() {
    return cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
}

// Get cart item count
function getCartItemCount() {
    return cart.reduce((sum, item) => sum + item.quantity, 0);
}
