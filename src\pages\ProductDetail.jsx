import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import ProductCard from '../components/ProductCard';
import { products } from '../data/products';
import './ProductDetail.css';

function ProductDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  
  const [product, setProduct] = useState(null);
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [relatedProducts, setRelatedProducts] = useState([]);

  useEffect(() => {
    const productId = parseInt(id);
    const foundProduct = products.find(p => p.id === productId);
    
    if (!foundProduct) {
      navigate('/products');
      return;
    }
    
    setProduct(foundProduct);
    setSelectedSize(foundProduct.sizes[0]);
    setSelectedColor(foundProduct.colors[0]);
    
    // Load related products
    const related = products
      .filter(p => p.category === foundProduct.category && p.id !== foundProduct.id)
      .slice(0, 4);
    setRelatedProducts(related);
  }, [id, navigate]);

  const handleAddToCart = () => {
    if (!selectedSize || !selectedColor) {
      alert('Please select size and color');
      return;
    }
    
    setIsLoading(true);
    addToCart(product, selectedSize, selectedColor, quantity);
    
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={i} className="fas fa-star"></i>);
    }

    if (hasHalfStar) {
      stars.push(<i key="half" className="fas fa-star-half-alt"></i>);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<i key={`empty-${i}`} className="far fa-star"></i>);
    }

    return stars;
  };

  if (!product) {
    return (
      <div className="loading-container">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Loading product...</p>
      </div>
    );
  }

  return (
    <main className="product-detail-page">
      <div className="container">
        {/* Breadcrumb */}
        <nav className="breadcrumb">
          <Link to="/">Home</Link>
          <span>/</span>
          <Link to="/products">Products</Link>
          <span>/</span>
          <span>{product.name}</span>
        </nav>

        {/* Product Detail */}
        <div className="product-detail">
          <div className="product-images">
            <div className="main-image">
              <img 
                src={product.image} 
                alt={product.name}
                onError={(e) => {
                  e.target.src = `https://via.placeholder.com/500x500?text=${encodeURIComponent(product.name)}`;
                }}
              />
            </div>
          </div>

          <div className="product-info">
            <div className="product-category">
              {product.category.charAt(0).toUpperCase() + product.category.slice(1)}
            </div>
            
            <h1>{product.name}</h1>
            
            <div className="product-rating">
              <div className="stars">
                {renderStars(product.rating)}
              </div>
              <span className="rating-text">
                {product.rating} ({product.reviews} reviews)
              </span>
            </div>
            
            <div className="product-price">
              {formatPrice(product.price)}
            </div>
            
            <div className="product-description">
              <p>{product.description}</p>
            </div>

            <div className="product-options">
              <div className="option-group">
                <label htmlFor="size-select">Size:</label>
                <select
                  id="size-select"
                  value={selectedSize}
                  onChange={(e) => setSelectedSize(e.target.value)}
                >
                  {product.sizes.map(size => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>

              <div className="option-group">
                <label htmlFor="color-select">Color:</label>
                <select
                  id="color-select"
                  value={selectedColor}
                  onChange={(e) => setSelectedColor(e.target.value)}
                >
                  {product.colors.map(color => (
                    <option key={color} value={color}>{color}</option>
                  ))}
                </select>
              </div>

              <div className="option-group">
                <label htmlFor="quantity">Quantity:</label>
                <input
                  type="number"
                  id="quantity"
                  min="1"
                  max="10"
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value))}
                />
              </div>
            </div>

            <div className="product-actions">
              <button 
                className={`add-to-cart-btn ${isLoading ? 'loading' : ''}`}
                onClick={handleAddToCart}
                disabled={isLoading || !product.inStock}
              >
                {isLoading ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i> Adding to Cart...
                  </>
                ) : product.inStock ? (
                  <>
                    <i className="fas fa-shopping-cart"></i> Add to Cart
                  </>
                ) : (
                  <>
                    <i className="fas fa-times"></i> Out of Stock
                  </>
                )}
              </button>
              
              <button className="wishlist-btn">
                <i className="far fa-heart"></i> Add to Wishlist
              </button>
            </div>

            <div className="product-features">
              <div className="feature">
                <i className="fas fa-truck"></i>
                <span>Free shipping on orders over $50</span>
              </div>
              <div className="feature">
                <i className="fas fa-undo"></i>
                <span>30-day return policy</span>
              </div>
              <div className="feature">
                <i className="fas fa-shield-alt"></i>
                <span>Quality guarantee</span>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <section className="related-products">
            <h2>You Might Also Like</h2>
            <div className="product-grid">
              {relatedProducts.map(product => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </section>
        )}
      </div>
    </main>
  );
}

export default ProductDetail;
