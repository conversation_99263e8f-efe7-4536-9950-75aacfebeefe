.header {
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-brand a {
  text-decoration: none;
  color: inherit;
}

.nav-brand h1 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin: 0;
}

.nav-brand i {
  margin-right: 0.5rem;
  color: #3498db;
}

.nav-menu ul {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 0;
  position: relative;
}

.nav-menu a:hover,
.nav-menu a.active {
  color: #3498db;
}

.nav-menu a.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 2px;
  background: #3498db;
}

.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-btn,
.cart-btn,
.mobile-menu-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.5rem;
  border-radius: 50%;
  position: relative;
}

.search-btn:hover,
.cart-btn:hover,
.mobile-menu-btn:hover {
  background: #f8f9fa;
  color: #3498db;
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.mobile-menu-btn {
  display: none;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 999;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .header .container {
    height: auto;
    padding: 1rem 20px;
    flex-wrap: wrap;
  }

  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: white;
    transition: all 0.3s ease;
    z-index: 1001;
  }

  .nav-menu.mobile-open {
    left: 0;
  }

  .nav-menu ul {
    flex-direction: column;
    padding: 2rem;
    gap: 1rem;
  }

  .nav-menu a {
    font-size: 1.1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
  }

  .mobile-menu-btn {
    display: block;
  }

  .search-btn {
    display: none;
  }
}

@media (max-width: 480px) {
  .nav-brand h1 {
    font-size: 1.5rem;
  }
  
  .header .container {
    padding: 0.8rem 15px;
  }
}
