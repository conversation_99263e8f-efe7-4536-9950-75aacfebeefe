.checkout-page {
  padding: 2rem 0;
  min-height: calc(100vh - 140px);
}

.empty-checkout {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  max-width: 500px;
  margin: 0 auto;
}

.empty-checkout i {
  font-size: 4rem;
  color: #ddd;
  margin-bottom: 1rem;
}

.empty-checkout h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.empty-checkout p {
  color: #666;
  margin-bottom: 2rem;
}

.checkout-steps {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  margin-bottom: 3rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #bdc3c7;
  transition: all 0.3s ease;
}

.step.active {
  color: #3498db;
}

.step-number {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: #bdc3c7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: #3498db;
}

.step-title {
  font-weight: 600;
}

.checkout-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
}

.form-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.form-section h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.form-group input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.order-review {
  color: #666;
  line-height: 1.6;
}

.order-review h3 {
  color: #2c3e50;
  margin: 1.5rem 0 0.5rem 0;
  font-weight: 600;
}

.order-review h3:first-child {
  margin-top: 0;
}

.order-summary {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  height: fit-content;
  position: sticky;
  top: 100px;
}

.order-summary h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.summary-items {
  margin-bottom: 1.5rem;
}

.summary-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.summary-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.summary-item img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.item-details {
  flex: 1;
}

.item-details h4 {
  margin: 0 0 0.3rem 0;
  font-size: 0.9rem;
  color: #2c3e50;
}

.item-details p {
  margin: 0;
  font-size: 0.8rem;
  color: #666;
}

.item-price {
  font-weight: 600;
  color: #e74c3c;
  align-self: flex-start;
}

.summary-totals {
  border-top: 2px solid #eee;
  padding-top: 1rem;
}

.total-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  color: #666;
}

.total-line.total {
  font-weight: 700;
  font-size: 1.2rem;
  color: #2c3e50;
  border-top: 1px solid #ddd;
  padding-top: 0.5rem;
  margin-top: 1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .checkout-content {
    grid-template-columns: 1fr;
  }
  
  .order-summary {
    position: static;
    order: -1;
  }
  
  .checkout-steps {
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .step-title {
    display: none;
  }
  
  .form-section {
    padding: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    justify-content: center;
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .checkout-page {
    padding: 1rem 0;
  }
  
  .form-section,
  .order-summary {
    margin: 0 1rem 2rem;
    padding: 1rem;
  }
  
  .checkout-steps {
    gap: 0.5rem;
    margin: 0 1rem 2rem;
  }
  
  .step-number {
    width: 30px;
    height: 30px;
  }
}
