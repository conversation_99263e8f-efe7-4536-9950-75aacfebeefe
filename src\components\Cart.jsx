import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import './Cart.css';

function Cart({ isOpen, onClose }) {
  const { items, totalPrice, removeFromCart, updateQuantity } = useCart();
  const navigate = useNavigate();

  const handleCheckout = () => {
    if (items.length === 0) {
      alert('Your cart is empty!');
      return;
    }
    onClose();
    navigate('/checkout');
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <>
      {/* Overlay */}
      {isOpen && <div className="cart-overlay" onClick={onClose}></div>}
      
      {/* Cart Sidebar */}
      <div className={`cart-sidebar ${isOpen ? 'open' : ''}`}>
        <div className="cart-header">
          <h3>Shopping Cart</h3>
          <button className="close-cart" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="cart-items">
          {items.length === 0 ? (
            <div className="empty-cart">
              <i className="fas fa-shopping-cart"></i>
              <p>Your cart is empty</p>
              <button className="continue-shopping" onClick={onClose}>
                Continue Shopping
              </button>
            </div>
          ) : (
            items.map((item, index) => (
              <div key={`${item.id}-${item.size}-${item.color}`} className="cart-item">
                <img 
                  src={item.image} 
                  alt={item.name}
                  onError={(e) => {
                    e.target.src = `https://via.placeholder.com/60x60?text=${encodeURIComponent(item.name)}`;
                  }}
                />
                <div className="cart-item-info">
                  <h4>{item.name}</h4>
                  <p className="item-details">
                    Size: {item.size} | Color: {item.color}
                  </p>
                  <div className="cart-item-controls">
                    <button 
                      className="quantity-btn"
                      onClick={() => updateQuantity(index, item.quantity - 1)}
                      disabled={item.quantity <= 1}
                    >
                      <i className="fas fa-minus"></i>
                    </button>
                    <span className="quantity">{item.quantity}</span>
                    <button 
                      className="quantity-btn"
                      onClick={() => updateQuantity(index, item.quantity + 1)}
                    >
                      <i className="fas fa-plus"></i>
                    </button>
                    <button 
                      className="remove-btn"
                      onClick={() => removeFromCart(index)}
                      title="Remove item"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                  <div className="cart-item-price">
                    {formatPrice(item.price * item.quantity)}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {items.length > 0 && (
          <div className="cart-footer">
            <div className="cart-total">
              <div className="total-line">
                <span>Subtotal:</span>
                <span>{formatPrice(totalPrice)}</span>
              </div>
              <div className="total-line">
                <span>Shipping:</span>
                <span>{totalPrice > 50 ? 'Free' : '$9.99'}</span>
              </div>
              <div className="total-line total">
                <span>Total:</span>
                <span>{formatPrice(totalPrice + (totalPrice > 50 ? 0 : 9.99))}</span>
              </div>
            </div>
            <button className="checkout-btn" onClick={handleCheckout}>
              Proceed to Checkout
            </button>
          </div>
        )}
      </div>
    </>
  );
}

export default Cart;
