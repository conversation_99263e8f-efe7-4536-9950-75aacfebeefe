import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import Cart from './Cart';
import './Header.css';

function Header() {
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { totalItems } = useCart();
  const location = useLocation();

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const isActiveLink = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  return (
    <>
      <header className="header">
        <div className="container">
          <div className="nav-brand">
            <Link to="/">
              <h1><i className="fas fa-camera"></i> StyleSnap</h1>
            </Link>
          </div>
          
          <nav className={`nav-menu ${isMobileMenuOpen ? 'mobile-open' : ''}`}>
            <ul>
              <li><Link to="/" className={isActiveLink('/')}>Home</Link></li>
              <li><Link to="/products" className={isActiveLink('/products')}>Products</Link></li>
              <li><Link to="/about" className={isActiveLink('/about')}>About</Link></li>
              <li><Link to="/contact" className={isActiveLink('/contact')}>Contact</Link></li>
            </ul>
          </nav>

          <div className="nav-actions">
            <button className="search-btn" title="Search">
              <i className="fas fa-search"></i>
            </button>
            <button className="cart-btn" onClick={toggleCart} title="Shopping Cart">
              <i className="fas fa-shopping-cart"></i>
              {totalItems > 0 && <span className="cart-count">{totalItems}</span>}
            </button>
            <button 
              className="mobile-menu-btn"
              onClick={toggleMobileMenu}
              title="Menu"
            >
              <i className={`fas ${isMobileMenuOpen ? 'fa-times' : 'fa-bars'}`}></i>
            </button>
          </div>
        </div>
      </header>

      {/* Cart Sidebar */}
      <Cart isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
      
      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="mobile-overlay" onClick={toggleMobileMenu}></div>
      )}
    </>
  );
}

export default Header;
