import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { products } from '../data/products';
import './Home.css';

function Home() {
  const navigate = useNavigate();
  
  const featuredProducts = products.filter(product => product.featured);
  
  const handleCategoryClick = (category) => {
    navigate(`/products?category=${category}`);
  };

  return (
    <main className="home-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Discover Your Style</h1>
          <p>Premium clothing collection for the modern wardrobe</p>
          <Link to="/products" className="cta-button">
            Shop Now <i className="fas fa-arrow-right"></i>
          </Link>
        </div>
        <div className="hero-image">
          <img 
            src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop" 
            alt="Fashion Collection"
          />
        </div>
      </section>

      {/* Categories Section */}
      <section className="categories">
        <div className="container">
          <h2>Shop by Category</h2>
          <div className="category-grid">
            <div className="category-card" onClick={() => handleCategoryClick('men')}>
              <div className="category-image">
                <img 
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop" 
                  alt="Men's Clothing"
                />
              </div>
              <div className="category-info">
                <h3>Men's Collection</h3>
                <p>Discover our latest men's fashion</p>
                <span className="category-link">
                  Shop Men's <i className="fas fa-arrow-right"></i>
                </span>
              </div>
            </div>
            
            <div className="category-card" onClick={() => handleCategoryClick('women')}>
              <div className="category-image">
                <img 
                  src="https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400&h=300&fit=crop" 
                  alt="Women's Clothing"
                />
              </div>
              <div className="category-info">
                <h3>Women's Collection</h3>
                <p>Elegant styles for every occasion</p>
                <span className="category-link">
                  Shop Women's <i className="fas fa-arrow-right"></i>
                </span>
              </div>
            </div>
            
            <div className="category-card" onClick={() => handleCategoryClick('accessories')}>
              <div className="category-image">
                <img 
                  src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=300&fit=crop" 
                  alt="Accessories"
                />
              </div>
              <div className="category-info">
                <h3>Accessories</h3>
                <p>Complete your look with our accessories</p>
                <span className="category-link">
                  Shop Accessories <i className="fas fa-arrow-right"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="featured-products">
        <div className="container">
          <div className="section-header">
            <h2>Featured Products</h2>
            <p>Handpicked items from our latest collection</p>
          </div>
          <div className="product-grid">
            {featuredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
          <div className="section-footer">
            <Link to="/products" className="view-all-btn">
              View All Products <i className="fas fa-arrow-right"></i>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">
                <i className="fas fa-shipping-fast"></i>
              </div>
              <h3>Free Shipping</h3>
              <p>Free shipping on orders over $50</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">
                <i className="fas fa-undo"></i>
              </div>
              <h3>Easy Returns</h3>
              <p>30-day hassle-free return policy</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">
                <i className="fas fa-shield-alt"></i>
              </div>
              <h3>Quality Guarantee</h3>
              <p>Premium quality guaranteed on all products</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">
                <i className="fas fa-headset"></i>
              </div>
              <h3>24/7 Support</h3>
              <p>Customer support available around the clock</p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}

export default Home;
