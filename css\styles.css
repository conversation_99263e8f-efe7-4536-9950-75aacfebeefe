/* CSS Custom Properties */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-color: #333;
  --text-light: #666;
  --background-color: #f8f9fa;
  --white: #ffffff;
  --border-color: #ddd;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  background: var(--white);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 20px;
}

.nav-brand h1 {
  color: var(--primary-color);
  font-size: 1.8rem;
}

.nav-brand a {
  text-decoration: none;
  color: inherit;
}

.nav-menu ul {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
}

.nav-menu a:hover,
.nav-menu a.active {
  color: var(--secondary-color);
}

.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-btn,
.cart-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.cart-btn {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--accent-color);
  color: var(--white);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Hero Section */
.hero {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: var(--white);
  padding: 6rem 0;
  text-align: center;
}

.hero-content h2 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-button {
  display: inline-block;
  background: var(--accent-color);
  color: var(--white);
  padding: 1rem 2rem;
  text-decoration: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  transition: var(--transition);
}

.cta-button:hover {
  background: #c0392b;
  transform: translateY(-2px);
}

/* Categories Section */
.categories {
  padding: 4rem 0;
}

.categories h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: var(--primary-color);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.category-card {
  background: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: var(--transition);
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.category-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.category-card h3 {
  padding: 1.5rem;
  text-align: center;
  color: var(--primary-color);
}

/* Featured Products */
.featured-products {
  padding: 4rem 0;
  background: var(--white);
}

.featured-products h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: var(--primary-color);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.product-card {
  background: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.product-card img {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.product-info {
  padding: 1.5rem;
}

.product-info h3 {
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.product-info p {
  color: var(--text-light);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.product-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.add-to-cart {
  width: 100%;
  background: var(--secondary-color);
  color: var(--white);
  border: none;
  padding: 0.8rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.add-to-cart:hover {
  background: #2980b9;
}

/* Shopping Cart Sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--white);
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.cart-sidebar.open {
  right: 0;
}

.cart-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-cart {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-color);
}

.cart-items {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.cart-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.cart-item-info {
  flex: 1;
}

.cart-item-info h4 {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.cart-item-price {
  color: var(--accent-color);
  font-weight: 600;
}

.cart-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.cart-total {
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.checkout-btn {
  width: 100%;
  background: var(--accent-color);
  color: var(--white);
  border: none;
  padding: 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.checkout-btn:hover {
  background: #c0392b;
}

/* Footer */
.footer {
  background: var(--primary-color);
  color: var(--white);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  margin-bottom: 1rem;
  color: var(--white);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  transition: var(--transition);
}

.footer-section a:hover {
  color: var(--white);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  font-size: 1.5rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #34495e;
  color: #bdc3c7;
}

/* Products Page Styles */
.products-page {
  padding: 2rem 0;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.filters {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: var(--primary-color);
}

.filter-group select,
.filter-group input {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

/* Product Detail Page */
.product-detail-page {
  padding: 2rem 0;
}

.breadcrumb {
  margin-bottom: 2rem;
  color: var(--text-light);
}

.breadcrumb a {
  color: var(--secondary-color);
  text-decoration: none;
}

.product-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 4rem;
}

.product-images .main-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.product-info h1 {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.product-price .price {
  font-size: 2rem;
  color: var(--accent-color);
  font-weight: 600;
}

.product-description {
  margin: 2rem 0;
  color: var(--text-light);
  line-height: 1.8;
}

.product-options {
  margin: 2rem 0;
}

.option-group {
  margin-bottom: 1.5rem;
}

.option-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.option-group select,
.option-group input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.product-actions {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
}

.add-to-cart-btn,
.wishlist-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.add-to-cart-btn {
  background: var(--secondary-color);
  color: var(--white);
}

.add-to-cart-btn:hover {
  background: #2980b9;
}

.wishlist-btn {
  background: var(--white);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.wishlist-btn:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.product-features {
  margin-top: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.feature i {
  color: var(--secondary-color);
}

/* Checkout Page */
.checkout-page {
  padding: 2rem 0;
}

.checkout-steps {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-light);
}

.step.active {
  color: var(--secondary-color);
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.step.active .step-number {
  background: var(--secondary-color);
  color: var(--white);
}

.checkout-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.form-section {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
}

.form-section h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.btn-primary {
  background: var(--secondary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: var(--white);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

.order-summary {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  height: fit-content;
}

.order-summary h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.summary-items {
  margin-bottom: 1.5rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.summary-totals {
  border-top: 2px solid var(--border-color);
  padding-top: 1rem;
}

.total-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.total-line.total {
  font-weight: 600;
  font-size: 1.2rem;
  color: var(--primary-color);
  border-top: 1px solid var(--border-color);
  padding-top: 0.5rem;
  margin-top: 1rem;
}

/* About and Contact Pages */
.about-page,
.contact-page {
  padding: 2rem 0;
}

.about-content,
.contact-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-story,
.about-values,
.about-team {
  margin-bottom: 3rem;
}

.about-story h2,
.about-values h2,
.about-team h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.value-card {
  text-align: center;
  padding: 2rem;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.value-card i {
  font-size: 3rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.about-team ul {
  list-style: none;
}

.about-team li {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.about-team i {
  color: var(--secondary-color);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.contact-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-item i {
  font-size: 1.5rem;
  color: var(--secondary-color);
  margin-top: 0.5rem;
}

.contact-form {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header .container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu ul {
    gap: 1rem;
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .category-grid {
    grid-template-columns: 1fr;
  }

  .cart-sidebar {
    width: 100%;
    right: -100%;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .product-detail {
    grid-template-columns: 1fr;
  }

  .checkout-content {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
  }

  .filters {
    flex-direction: column;
    gap: 1rem;
  }

  .product-actions {
    flex-direction: column;
  }
}
