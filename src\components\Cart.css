.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1999;
}

.cart-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -5px 0 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.cart-sidebar.open {
  right: 0;
}

.cart-header {
  padding: 1.5rem;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.cart-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-cart {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-cart:hover {
  background: #e9ecef;
  color: #333;
}

.cart-items {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.empty-cart {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
}

.empty-cart i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #ddd;
}

.empty-cart p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.continue-shopping {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.continue-shopping:hover {
  background: #2980b9;
}

.cart-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.cart-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cart-item-info h4 {
  margin: 0;
  font-size: 0.9rem;
  color: #2c3e50;
  line-height: 1.3;
}

.item-details {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

.cart-item-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: auto;
}

.quantity-btn {
  background: #3498db;
  color: white;
  border: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.quantity-btn:hover:not(:disabled) {
  background: #2980b9;
}

.quantity-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
}

.remove-btn {
  background: #e74c3c;
  color: white;
  border: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  margin-left: 0.5rem;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #c0392b;
}

.cart-item-price {
  font-weight: 600;
  color: #e74c3c;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.cart-footer {
  padding: 1.5rem;
  border-top: 1px solid #ddd;
  background: #f8f9fa;
}

.cart-total {
  margin-bottom: 1.5rem;
}

.total-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  color: #666;
}

.total-line.total {
  font-weight: 600;
  font-size: 1.1rem;
  color: #2c3e50;
  border-top: 1px solid #ddd;
  padding-top: 0.5rem;
  margin-top: 1rem;
}

.checkout-btn {
  width: 100%;
  background: #e74c3c;
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.checkout-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100%;
    right: -100%;
  }
  
  .cart-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .cart-item img {
    width: 80px;
    height: 80px;
  }
  
  .cart-item-controls {
    justify-content: space-between;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .cart-sidebar {
    width: 100vw;
  }
}
