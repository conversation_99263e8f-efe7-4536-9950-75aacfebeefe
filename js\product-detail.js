// Product detail page functionality
let currentProduct = null;

document.addEventListener('DOMContentLoaded', function() {
    // Wait for products to load from main.js
    setTimeout(() => {
        loadProductDetail();
    }, 100);
});

function loadProductDetail() {
    const productId = parseInt(getUrlParameter('id'));
    if (!productId) {
        window.location.href = 'products.html';
        return;
    }
    
    currentProduct = products.find(p => p.id === productId);
    if (!currentProduct) {
        alert('Product not found!');
        window.location.href = 'products.html';
        return;
    }
    
    displayProductDetail();
    loadRelatedProducts();
}

function displayProductDetail() {
    // Update breadcrumb
    const breadcrumb = document.getElementById('product-name-breadcrumb');
    if (breadcrumb) breadcrumb.textContent = currentProduct.name;
    
    // Update main product image
    const mainImage = document.getElementById('main-product-image');
    if (mainImage) {
        mainImage.src = currentProduct.image;
        mainImage.alt = currentProduct.name;
        mainImage.onerror = function() {
            this.src = `https://via.placeholder.com/500x500?text=${encodeURIComponent(currentProduct.name)}`;
        };
    }
    
    // Update product info
    const productName = document.getElementById('product-name');
    const productPrice = document.getElementById('product-price');
    const productDescription = document.getElementById('product-description');
    
    if (productName) productName.textContent = currentProduct.name;
    if (productPrice) productPrice.textContent = `$${currentProduct.price.toFixed(2)}`;
    if (productDescription) productDescription.textContent = currentProduct.description;
    
    // Populate size options
    const sizeSelect = document.getElementById('size-select');
    if (sizeSelect) {
        sizeSelect.innerHTML = '<option value="">Select Size</option>' +
            currentProduct.sizes.map(size => `<option value="${size}">${size}</option>`).join('');
    }
    
    // Populate color options
    const colorSelect = document.getElementById('color-select');
    if (colorSelect) {
        colorSelect.innerHTML = '<option value="">Select Color</option>' +
            currentProduct.colors.map(color => `<option value="${color}">${color}</option>`).join('');
    }
    
    // Update page title
    document.title = `${currentProduct.name} - StyleSnap`;
}

function loadRelatedProducts() {
    const relatedContainer = document.getElementById('related-products');
    if (!relatedContainer) return;
    
    // Get products from the same category, excluding current product
    const relatedProducts = products
        .filter(p => p.category === currentProduct.category && p.id !== currentProduct.id)
        .slice(0, 4); // Show max 4 related products
    
    if (relatedProducts.length === 0) {
        relatedContainer.style.display = 'none';
        return;
    }
    
    relatedContainer.innerHTML = relatedProducts.map(product => createProductCard(product)).join('');
}

function addToCartFromDetail() {
    const sizeSelect = document.getElementById('size-select');
    const colorSelect = document.getElementById('color-select');
    const quantityInput = document.getElementById('quantity');
    
    const selectedSize = sizeSelect ? sizeSelect.value : currentProduct.sizes[0];
    const selectedColor = colorSelect ? colorSelect.value : currentProduct.colors[0];
    const quantity = quantityInput ? parseInt(quantityInput.value) : 1;
    
    // Validate selections
    if (!selectedSize) {
        alert('Please select a size.');
        return;
    }
    
    if (!selectedColor) {
        alert('Please select a color.');
        return;
    }
    
    if (quantity < 1 || quantity > 10) {
        alert('Please select a valid quantity (1-10).');
        return;
    }
    
    // Add to cart
    addToCart(currentProduct.id, selectedSize, selectedColor, quantity);
    
    // Show success message
    showAddToCartSuccess();
}

function showAddToCartSuccess() {
    const button = document.querySelector('.add-to-cart-btn');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-check"></i> Added to Cart!';
    button.style.background = '#27ae60';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.style.background = '';
        button.disabled = false;
    }, 2000);
}

// Image gallery functionality (for future enhancement)
function changeMainImage(imageSrc) {
    const mainImage = document.getElementById('main-product-image');
    if (mainImage) {
        mainImage.src = imageSrc;
    }
}

// Product zoom functionality (for future enhancement)
function initializeImageZoom() {
    const mainImage = document.getElementById('main-product-image');
    if (!mainImage) return;
    
    mainImage.addEventListener('click', function() {
        // Create modal for zoomed image
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3000;
            cursor: pointer;
        `;
        
        const zoomedImage = document.createElement('img');
        zoomedImage.src = this.src;
        zoomedImage.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        `;
        
        modal.appendChild(zoomedImage);
        document.body.appendChild(modal);
        
        modal.addEventListener('click', function() {
            document.body.removeChild(modal);
        });
    });
}

// Initialize image zoom when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeImageZoom();
    }, 500);
});

// Wishlist functionality (basic implementation)
let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

function toggleWishlist(productId) {
    const index = wishlist.indexOf(productId);
    const wishlistBtn = document.querySelector('.wishlist-btn');
    
    if (index > -1) {
        // Remove from wishlist
        wishlist.splice(index, 1);
        if (wishlistBtn) {
            wishlistBtn.innerHTML = '<i class="far fa-heart"></i> Add to Wishlist';
        }
    } else {
        // Add to wishlist
        wishlist.push(productId);
        if (wishlistBtn) {
            wishlistBtn.innerHTML = '<i class="fas fa-heart"></i> Remove from Wishlist';
        }
    }
    
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
}

// Update wishlist button state
function updateWishlistButton() {
    if (!currentProduct) return;
    
    const wishlistBtn = document.querySelector('.wishlist-btn');
    if (!wishlistBtn) return;
    
    const isInWishlist = wishlist.includes(currentProduct.id);
    
    if (isInWishlist) {
        wishlistBtn.innerHTML = '<i class="fas fa-heart"></i> Remove from Wishlist';
        wishlistBtn.onclick = () => toggleWishlist(currentProduct.id);
    } else {
        wishlistBtn.innerHTML = '<i class="far fa-heart"></i> Add to Wishlist';
        wishlistBtn.onclick = () => toggleWishlist(currentProduct.id);
    }
}

// Call updateWishlistButton after product loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        updateWishlistButton();
    }, 200);
});

// Product review functionality (basic structure for future enhancement)
function loadProductReviews(productId) {
    // This would typically load reviews from a database
    // For now, we'll just show a placeholder
    const reviewsContainer = document.getElementById('product-reviews');
    if (reviewsContainer) {
        reviewsContainer.innerHTML = `
            <div class="reviews-placeholder">
                <h3>Customer Reviews</h3>
                <p>Reviews feature coming soon!</p>
            </div>
        `;
    }
}

// Stock checking (basic implementation)
function checkStock(productId, size, color) {
    // In a real application, this would check actual inventory
    // For demo purposes, we'll assume everything is in stock
    return true;
}

// Price calculation with discounts (for future enhancement)
function calculateDiscountedPrice(originalPrice, discountPercent = 0) {
    if (discountPercent > 0) {
        return originalPrice * (1 - discountPercent / 100);
    }
    return originalPrice;
}
