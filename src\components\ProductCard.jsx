import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import './ProductCard.css';

function ProductCard({ product, showQuickAdd = true }) {
  const [isLoading, setIsLoading] = useState(false);
  const { addToCart } = useCart();
  const navigate = useNavigate();

  const handleQuickAdd = (e) => {
    e.stopPropagation();
    setIsLoading(true);
    
    // Use default size and color for quick add
    const defaultSize = product.sizes[0];
    const defaultColor = product.colors[0];
    
    addToCart(product, defaultSize, defaultColor, 1);
    
    // Show loading state briefly
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  };

  const handleCardClick = () => {
    navigate(`/product/${product.id}`);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={i} className="fas fa-star"></i>);
    }

    if (hasHalfStar) {
      stars.push(<i key="half" className="fas fa-star-half-alt"></i>);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<i key={`empty-${i}`} className="far fa-star"></i>);
    }

    return stars;
  };

  return (
    <div className="product-card" onClick={handleCardClick}>
      <div className="product-image">
        <img 
          src={product.image} 
          alt={product.name}
          onError={(e) => {
            e.target.src = `https://via.placeholder.com/300x300?text=${encodeURIComponent(product.name)}`;
          }}
        />
        {!product.inStock && <div className="out-of-stock">Out of Stock</div>}
        {product.featured && <div className="featured-badge">Featured</div>}
      </div>
      
      <div className="product-info">
        <div className="product-category">
          {product.category.charAt(0).toUpperCase() + product.category.slice(1)}
        </div>
        <h3 className="product-name">{product.name}</h3>
        <p className="product-description">{product.description}</p>
        
        <div className="product-rating">
          <div className="stars">
            {renderStars(product.rating)}
          </div>
          <span className="review-count">({product.reviews})</span>
        </div>
        
        <div className="product-meta">
          <span className="sizes-available">{product.sizes.length} sizes</span>
          <span className="colors-available">{product.colors.length} colors</span>
        </div>
        
        <div className="product-price">
          {formatPrice(product.price)}
        </div>
        
        {showQuickAdd && product.inStock && (
          <div className="product-actions">
            <button 
              className={`quick-add-btn ${isLoading ? 'loading' : ''}`}
              onClick={handleQuickAdd}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <i className="fas fa-spinner fa-spin"></i> Adding...
                </>
              ) : (
                <>
                  <i className="fas fa-shopping-cart"></i> Quick Add
                </>
              )}
            </button>
            <button 
              className="view-details-btn"
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/product/${product.id}`);
              }}
            >
              <i className="fas fa-eye"></i> Details
            </button>
          </div>
        )}
        
        {!product.inStock && (
          <div className="product-actions">
            <button className="out-of-stock-btn" disabled>
              <i className="fas fa-times"></i> Out of Stock
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default ProductCard;
