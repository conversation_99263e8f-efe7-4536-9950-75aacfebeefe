.about-page {
  padding: 2rem 0;
  min-height: calc(100vh - 140px);
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-story,
.about-values,
.about-team {
  margin-bottom: 4rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.about-story h2,
.about-values h2,
.about-team h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  font-weight: 700;
}

.about-story p {
  color: #666;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.value-card {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.value-card i {
  font-size: 3rem;
  color: #3498db;
  margin-bottom: 1rem;
}

.value-card h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-weight: 600;
}

.value-card p {
  color: #666;
  line-height: 1.6;
}

.about-team ul {
  list-style: none;
  padding: 0;
}

.about-team li {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.about-team li:hover {
  background: #e9ecef;
}

.about-team i {
  color: #3498db;
  font-size: 1.2rem;
  width: 20px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .about-story,
  .about-values,
  .about-team {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .values-grid {
    grid-template-columns: 1fr;
  }
  
  .value-card {
    padding: 1.5rem;
  }
  
  .value-card i {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .about-page {
    padding: 1rem 0;
  }
  
  .about-story,
  .about-values,
  .about-team {
    padding: 1rem;
    margin: 0 1rem 2rem;
  }
  
  .about-story h2,
  .about-values h2,
  .about-team h2 {
    font-size: 1.5rem;
  }
}
